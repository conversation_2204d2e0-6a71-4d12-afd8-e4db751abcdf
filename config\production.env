# SentryCoin v4.0 - PRODUCTION DEPLOYMENT CONFIGURATION
# LIVE PAPER TRADING ENVIRONMENT
# 
# ⚠️  CRITICAL: This configuration enables live paper trading
# ⚠️  All trades are simulated but use real market data
# ⚠️  No actual money is at risk

# ================================
# PRODUCTION TRADING CONFIGURATION
# ================================

# Trading Symbol (Validated in live cascade event)
SYMBOL=SPKUSDT

# Exchange (Binance primary, Coinbase fallback)
EXCHANGE=binance

# Trading Mode - PAPER TRADING ENABLED FOR SAFETY
PAPER_TRADING=true

# Strategy Enablement - TRIFECTA ONLY (proven in live event)
TRIFECTA_TRADING_ENABLED=true
SQUEEZE_TRADING_ENABLED=false
PRESSURE_SPIKE_ALERTS_ENABLED=true

# ================================
# VALIDATED THRESHOLDS (From Live Analysis)
# ================================

# Market pressure threshold (validated: 3.18x triggered first signal)
PRESSURE_THRESHOLD=3.0

# Liquidity threshold (validated: 156k liquidity in live event)
LIQUIDITY_THRESHOLD=100000

# Momentum thresholds (validated: -0.755% triggered in live cascade)
STRONG_MOMENTUM_THRESHOLD=-0.3
WEAK_MOMENTUM_THRESHOLD=-0.1

# ================================
# PRODUCTION RISK MANAGEMENT
# ================================

# Trifecta Strategy (SHORT positions) - Conservative settings
TRIFECTA_MAX_POSITION=1000
TRIFECTA_STOP_LOSS=2.0
TRIFECTA_TAKE_PROFIT=5.0

# Squeeze Strategy (LONG positions) - Disabled for initial deployment
SQUEEZE_MAX_POSITION=0
SQUEEZE_STOP_LOSS=1.5
SQUEEZE_TAKE_PROFIT=3.0
SQUEEZE_TIME_EXIT=300

# ================================
# PRODUCTION MONITORING
# ================================

# Alert cooldown period (prevent spam during cascades)
COOLDOWN_MINUTES=5

# Order book analysis depth
ORDER_BOOK_DEPTH=50

# Logging level for production
LOG_LEVEL=info

# Environment mode
NODE_ENV=production

# Server port
PORT=3000

# ================================
# PERFORMANCE MONITORING
# ================================

# Enable detailed performance tracking
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_DETAILED_LOGGING=true
ENABLE_SIGNAL_VALIDATION=true

# Statistics reporting interval (minutes)
STATS_REPORT_INTERVAL=60

# Memory usage monitoring
ENABLE_MEMORY_MONITORING=true
MEMORY_ALERT_THRESHOLD=80

# ================================
# TELEGRAM ALERTS (REQUIRED)
# ================================

# Telegram Bot Configuration
# TELEGRAM_BOT_TOKEN=your_production_bot_token
# TELEGRAM_CHAT_ID=your_production_chat_id

# Alert frequency control
MAX_ALERTS_PER_HOUR=10
ENABLE_SIGNAL_CONFIRMATIONS=true

# ================================
# DEPLOYMENT METADATA
# ================================

DEPLOYMENT_VERSION=4.0.0
DEPLOYMENT_DATE=2025-07-24
DEPLOYMENT_ENVIRONMENT=production
DEPLOYMENT_STATUS=live_paper_trading
VALIDATED_BY=live_cascade_event_analysis
