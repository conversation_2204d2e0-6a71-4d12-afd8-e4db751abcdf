/**
 * SentryCoin v4.5 - Whale Watchlist Intelligence System
 *
 * Monitors specific whale addresses from top 50 holders forensic analysis
 * Implements three-tier threat level system for CASCADE_HUNTER strategy
 * Provides predictive intelligence based on on-chain whale movements
 */

import EventEmitter from 'events';
import axios from 'axios';
import { getISTTime, parseIntEnv } from '../utils/index.js';

export default class OnChainMonitor extends EventEmitter {
  constructor(config = {}) {
    super();

    this.config = config;
    this.symbol = 'SPK';
    this.contractAddress = '******************************************'; // SPK token contract
    this.isRunning = false;

    // API Configuration
    this.etherscanApiKey = process.env.ETHERSCAN_API_KEY;
    this.moralisApiKey = process.env.MORALIS_API_KEY;
    this.alchemyApiKey = process.env.ALCHEMY_API_KEY;

    // v4.5 Whale Watchlist (Top 50 holders forensic analysis)
    this.whaleWatchlist = new Set([
      process.env.WHALE_ADDRESS_1?.toLowerCase(),
      process.env.WHALE_ADDRESS_2?.toLowerCase(),
      process.env.WHALE_ADDRESS_3?.toLowerCase(),
      process.env.WHALE_ADDRESS_4?.toLowerCase(),
      process.env.WHALE_ADDRESS_5?.toLowerCase(),
      process.env.WHALE_ADDRESS_6?.toLowerCase(),
      process.env.WHALE_ADDRESS_7?.toLowerCase(),
      process.env.WHALE_ADDRESS_8?.toLowerCase(),
    ].filter(addr => addr && addr !== 'undefined'));

    // Threat level thresholds
    this.whaleDumpThresholdHigh = parseIntEnv('WHALE_DUMP_THRESHOLD_HIGH', 500000);    // 500k from watchlist
    this.whaleDumpThresholdMedium = parseIntEnv('WHALE_DUMP_THRESHOLD_MEDIUM', 3000000); // 3M from others
    this.dumpValidityHours = parseIntEnv('WHALE_DUMP_VALIDITY_HOURS', 6);
    this.monitoringInterval = parseIntEnv('ONCHAIN_MONITORING_INTERVAL', 30000);

    // Threat level state
    this.threatLevel = 'LOW'; // LOW, MEDIUM, HIGH
    this.lastThreatUpdate = 0;
    this.threatHistory = [];
    this.recentDumps = [];

    // Known exchange addresses (major CEX deposit addresses)
    this.exchangeAddresses = new Set([
      '0x3f5ce5fbfe3e9af3971dd833d26ba9b5c936f0be', // Binance 14
      '0xd551234ae421e3bcba99a0da6d736074f22192ff', // Binance 15
      '0x564286362092d8e7936f0549571a803b203aaced', // Binance 16
      '0x0681d8db095565fe8a346fa0277bffde9c0edbbf', // Binance 17
      '******************************************', // Coinbase 6
      '******************************************', // Coinbase 10
      '******************************************', // Gate.io 1
      '******************************************', // Gate.io 2
      '******************************************', // Bybit 1
      '******************************************', // KuCoin 1
      '******************************************', // KuCoin 2
      '******************************************', // Crypto.com 1
      '******************************************', // Crypto.com 2
    ]);

    console.log('🔗 Whale Watchlist Monitor v4.5 initialized');
    console.log(`   🐋 Monitoring ${this.whaleWatchlist.size} whale addresses from top 50 holders`);
    console.log(`   🚨 HIGH threat: ${(this.whaleDumpThresholdHigh/1000).toFixed(0)}k from watchlist`);
    console.log(`   ⚠️ MEDIUM threat: ${(this.whaleDumpThresholdMedium/1000000).toFixed(1)}M from others`);
    console.log(`   ⏰ Check interval: ${this.monitoringInterval/1000}s`);
    console.log(`   📡 APIs: ${this.etherscanApiKey ? 'Etherscan ✅' : ''} ${this.moralisApiKey ? 'Moralis ✅' : ''} ${this.alchemyApiKey ? 'Alchemy ✅' : ''}`);
  }

  /**
   * Start monitoring on-chain activity
   */
  async start() {
    if (this.isRunning) {
      console.log('⚠️ On-chain monitor already running');
      return;
    }

    this.isRunning = true;
    console.log('🚀 Starting on-chain monitoring...');

    // Start monitoring loop
    this.monitoringLoop();
  }

  /**
   * Stop monitoring
   */
  stop() {
    this.isRunning = false;
    console.log('⏹️ On-chain monitoring stopped');
  }

  /**
   * Main monitoring loop
   */
  async monitoringLoop() {
    while (this.isRunning) {
      try {
        await this.checkForWhaleMovements();
        await this.sleep(this.monitoringInterval);
      } catch (error) {
        console.error('❌ On-chain monitoring error:', error.message);
        await this.sleep(this.monitoringInterval * 2); // Back off on error
      }
    }
  }

  /**
   * Check for whale movements using watchlist intelligence
   */
  async checkForWhaleMovements() {
    const promises = [];

    // Primary: Etherscan API for whale watchlist monitoring
    if (this.etherscanApiKey) {
      promises.push(this.checkWatchlistMovements());
    }

    // Secondary: General whale movement detection
    if (this.moralisApiKey) {
      promises.push(this.checkMoralisTransactions());
    }

    if (this.alchemyApiKey) {
      promises.push(this.checkAlchemyTransactions());
    }

    // Fallback: Mock data for testing
    if (!this.etherscanApiKey && !this.moralisApiKey && !this.alchemyApiKey) {
      promises.push(this.checkMockWhaleData());
    }

    const results = await Promise.allSettled(promises);

    // Process results and update threat level
    results.forEach((result, index) => {
      if (result.status === 'rejected') {
        console.error(`❌ Whale check ${index} failed:`, result.reason);
      }
    });

    // Update threat level based on recent activity
    this.updateThreatLevel();
  }

  /**
   * v4.5 CORE: Monitor specific whale addresses from watchlist
   */
  async checkWatchlistMovements() {
    if (!this.etherscanApiKey) return;

    for (const whaleAddress of this.whaleWatchlist) {
      try {
        await this.checkSpecificWhaleAddress(whaleAddress);
        await this.sleep(200); // Rate limiting: 5 calls/sec
      } catch (error) {
        console.error(`❌ Failed to check whale ${whaleAddress}:`, error.message);
      }
    }
  }

  /**
   * Check specific whale address for exchange deposits
   */
  async checkSpecificWhaleAddress(whaleAddress) {
    try {
      const response = await axios.get('https://api.etherscan.io/api', {
        params: {
          module: 'account',
          action: 'tokentx',
          contractaddress: this.contractAddress,
          address: whaleAddress,
          startblock: 0,
          endblock: ********,
          sort: 'desc',
          apikey: this.etherscanApiKey
        },
        timeout: 10000
      });

      const transactions = response.data.result || [];

      // Check recent transactions (last 10)
      for (const tx of transactions.slice(0, 10)) {
        const amount = parseInt(tx.value) / Math.pow(10, parseInt(tx.tokenDecimal || 18));
        const isToExchange = this.exchangeAddresses.has(tx.to.toLowerCase());
        const isFromWatchlist = this.whaleWatchlist.has(tx.from.toLowerCase());

        if (isToExchange && isFromWatchlist && amount >= this.whaleDumpThresholdHigh) {
          await this.processWatchlistDump(tx, amount, 'HIGH');
        }
      }

    } catch (error) {
      console.error(`❌ Etherscan API error for ${whaleAddress}:`, error.message);
    }
  }

  /**
   * FREE Option 1: Check Etherscan API for large SPK movements
   * Free tier: 5 calls/second, 100k calls/day
   */
  async checkEtherscanTransactions() {
    if (!this.etherscanApiKey) return;

    try {
      // Get recent transactions for SPK token contract
      const response = await axios.get('https://api.etherscan.io/api', {
        params: {
          module: 'account',
          action: 'tokentx',
          contractaddress: '0x...', // SPK token contract address (need real address)
          startblock: 0,
          endblock: ********,
          sort: 'desc',
          apikey: this.etherscanApiKey
        },
        timeout: 10000
      });

      const transactions = response.data.result || [];

      for (const tx of transactions.slice(0, 50)) { // Check last 50 transactions
        const amount = parseInt(tx.value) / Math.pow(10, parseInt(tx.tokenDecimal));

        if (amount >= this.whaleThreshold) {
          await this.processEtherscanTransaction(tx, amount);
        }
      }

    } catch (error) {
      console.error('❌ Etherscan API error:', error.message);
    }
  }

  /**
   * FREE Option 2: Check Moralis API for whale movements
   * Free tier: 40k requests/month
   */
  async checkMoralisTransactions() {
    if (!this.moralisApiKey) return;

    try {
      const response = await axios.get('https://deep-index.moralis.io/api/v2/erc20/transfers', {
        params: {
          chain: 'eth',
          address: '0x...', // SPK token contract address
          limit: 50
        },
        headers: {
          'X-API-Key': this.moralisApiKey
        },
        timeout: 10000
      });

      const transfers = response.data.result || [];

      for (const transfer of transfers) {
        const amount = parseInt(transfer.value) / Math.pow(10, transfer.token_decimals);

        if (amount >= this.whaleThreshold) {
          await this.processMoralisTransaction(transfer, amount);
        }
      }

    } catch (error) {
      console.error('❌ Moralis API error:', error.message);
    }
  }

  /**
   * FREE Option 3: Check Alchemy API for whale movements
   * Free tier: 300M compute units/month
   */
  async checkAlchemyTransactions() {
    if (!this.alchemyApiKey) return;

    try {
      const response = await axios.post(`https://eth-mainnet.alchemyapi.io/v2/${this.alchemyApiKey}`, {
        jsonrpc: '2.0',
        method: 'alchemy_getAssetTransfers',
        params: [{
          fromBlock: 'latest',
          toBlock: 'latest',
          contractAddresses: ['0x...'], // SPK token contract
          category: ['erc20'],
          maxCount: 50
        }],
        id: 1
      }, {
        timeout: 10000
      });

      const transfers = response.data.result?.transfers || [];

      for (const transfer of transfers) {
        const amount = parseFloat(transfer.value);

        if (amount >= this.whaleThreshold) {
          await this.processAlchemyTransaction(transfer, amount);
        }
      }

    } catch (error) {
      console.error('❌ Alchemy API error:', error.message);
    }
  }

  /**
   * Process whale dump from watchlist (HIGH threat)
   */
  async processWatchlistDump(tx, amount, threatLevel) {
    const dumpData = {
      amount,
      from: tx.from,
      to: tx.to,
      timestamp: parseInt(tx.timeStamp) * 1000,
      hash: tx.hash,
      threatLevel,
      isWatchlistWhale: true,
      source: 'WATCHLIST_ETHERSCAN'
    };

    this.recentDumps.push(dumpData);
    console.log(`🚨 ${threatLevel} THREAT: Watchlist whale ${tx.from.substring(0,8)}... dumped ${(amount/1000).toFixed(0)}k SPK to exchange`);

    this.emit('WHALE_DUMP', dumpData);
    this.cleanupOldDumps();
  }

  /**
   * Update threat level based on recent whale activity
   */
  updateThreatLevel() {
    const now = Date.now();
    const validityWindow = this.dumpValidityHours * 60 * 60 * 1000;
    const recentDumps = this.recentDumps.filter(dump => now - dump.timestamp < validityWindow);

    let newThreatLevel = 'LOW';

    // Check for HIGH threat (watchlist whale dumps)
    const highThreatDumps = recentDumps.filter(dump =>
      dump.isWatchlistWhale && dump.amount >= this.whaleDumpThresholdHigh
    );

    if (highThreatDumps.length > 0) {
      newThreatLevel = 'HIGH';
    } else {
      // Check for MEDIUM threat (large non-watchlist dumps)
      const mediumThreatDumps = recentDumps.filter(dump =>
        dump.amount >= this.whaleDumpThresholdMedium
      );

      if (mediumThreatDumps.length > 0) {
        newThreatLevel = 'MEDIUM';
      }
    }

    // Update threat level if changed
    if (newThreatLevel !== this.threatLevel) {
      const previousLevel = this.threatLevel;
      this.threatLevel = newThreatLevel;
      this.lastThreatUpdate = now;

      console.log(`🎯 THREAT LEVEL UPDATED: ${previousLevel} → ${newThreatLevel}`);

      this.threatHistory.push({
        timestamp: now,
        previousLevel,
        newLevel: newThreatLevel,
        triggerDumps: recentDumps.length
      });

      this.emit('THREAT_LEVEL_CHANGE', {
        level: newThreatLevel,
        previousLevel,
        recentDumps: recentDumps.length,
        timestamp: now
      });
    }
  }

  /**
   * Get current threat level for CASCADE_HUNTER strategy
   */
  getThreatLevel() {
    return {
      level: this.threatLevel,
      lastUpdate: this.lastThreatUpdate,
      recentDumps: this.recentDumps.length,
      isValid: Date.now() - this.lastThreatUpdate < (this.dumpValidityHours * 60 * 60 * 1000)
    };
  }

  /**
   * Process Moralis transaction
   */
  async processMoralisTransaction(transfer, amount) {
    const isExchangeInflow = this.exchangeAddresses.has(transfer.to_address.toLowerCase());

    if (isExchangeInflow) {
      console.log(`🐋 MORALIS: ${amount} SPK whale inflow detected`);

      const inflowData = {
        amount,
        from: transfer.from_address,
        to: transfer.to_address,
        timestamp: new Date(transfer.block_timestamp).getTime(),
        hash: transfer.transaction_hash,
        type: 'EXCHANGE_INFLOW',
        source: 'MORALIS'
      };

      this.recentInflows.push(inflowData);
      this.emit('WHALE_INFLOW', inflowData);
      this.cleanupOldInflows();
    }
  }

  /**
   * Process Alchemy transaction
   */
  async processAlchemyTransaction(transfer, amount) {
    const isExchangeInflow = this.exchangeAddresses.has(transfer.to.toLowerCase());

    if (isExchangeInflow) {
      console.log(`🐋 ALCHEMY: ${amount} SPK whale inflow detected`);

      const inflowData = {
        amount,
        from: transfer.from,
        to: transfer.to,
        timestamp: Date.now(), // Alchemy doesn't provide timestamp in this format
        hash: transfer.hash,
        type: 'EXCHANGE_INFLOW',
        source: 'ALCHEMY'
      };

      this.recentInflows.push(inflowData);
      this.emit('WHALE_INFLOW', inflowData);
      this.cleanupOldInflows();
    }
  }

  /**
   * Mock whale data for testing (remove in production)
   */
  async checkMockWhaleData() {
    // This is for testing purposes - would be removed in production
    if (process.env.NODE_ENV === 'development' && Math.random() < 0.1) {
      const mockInflow = {
        amount: 5000000, // 5M SPK
        from: '0xmockwhale...',
        to: '0xbinance...',
        timestamp: Date.now(),
        hash: '0xmockhash...',
        type: 'EXCHANGE_INFLOW'
      };
      
      console.log('🧪 MOCK: Simulating whale inflow for testing');
      this.emit('WHALE_INFLOW', mockInflow);
    }
  }

  /**
   * Get recent whale inflows
   */
  getRecentInflows(timeWindowMs = 12 * 60 * 60 * 1000) { // 12 hours default
    const cutoff = Date.now() - timeWindowMs;
    return this.recentInflows.filter(inflow => inflow.timestamp > cutoff);
  }

  /**
   * Check if there are recent whale inflows
   */
  hasRecentWhaleInflows(timeWindowMs = 12 * 60 * 60 * 1000) {
    return this.getRecentInflows(timeWindowMs).length > 0;
  }

  /**
   * Get total recent inflow amount
   */
  getTotalRecentInflows(timeWindowMs = 12 * 60 * 60 * 1000) {
    return this.getRecentInflows(timeWindowMs)
      .reduce((total, inflow) => total + inflow.amount, 0);
  }

  /**
   * Clean up old dump records
   */
  cleanupOldDumps() {
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours
    const cutoff = Date.now() - maxAge;

    this.recentDumps = this.recentDumps.filter(dump => dump.timestamp > cutoff);
    this.threatHistory = this.threatHistory.filter(event => event.timestamp > cutoff);
  }

  /**
   * Utility function for delays
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get monitoring statistics
   */
  getStats() {
    const validityWindow = this.dumpValidityHours * 60 * 60 * 1000;
    const recentDumps = this.recentDumps.filter(dump =>
      Date.now() - dump.timestamp < validityWindow
    );

    const watchlistDumps = recentDumps.filter(dump => dump.isWatchlistWhale);
    const totalDumpAmount = recentDumps.reduce((sum, dump) => sum + dump.amount, 0);

    return {
      isRunning: this.isRunning,
      threatLevel: this.threatLevel,
      lastThreatUpdate: new Date(this.lastThreatUpdate).toISOString(),
      whaleWatchlistSize: this.whaleWatchlist.size,
      recentDumpCount: recentDumps.length,
      watchlistDumpCount: watchlistDumps.length,
      totalRecentDumpAmount: totalDumpAmount,
      hasHighThreat: this.threatLevel === 'HIGH',
      hasMediumThreat: this.threatLevel === 'MEDIUM',
      threatHistory: this.threatHistory.slice(-5), // Last 5 threat changes
      lastChecked: new Date().toISOString()
    };
  }
}
