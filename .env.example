# SentryCoin v4.0 - Environment Configuration Template
# Copy this file to .env and fill in your values

# ================================
# REQUIRED SETTINGS
# ================================

# Telegram Bot Configuration (REQUIRED)
# Create a bot by messaging @BotFather on Telegram
TELEGRAM_BOT_TOKEN=your_bot_token_here

# Telegram Chat ID where alerts will be sent
# Get this by messaging @userinfobot on Telegram
TELEGRAM_CHAT_ID=your_chat_id_here

# ================================
# TRADING CONFIGURATION
# ================================

# Trading Symbol
SYMBOL=SPKUSDT

# Exchange (binance, coinbase)
EXCHANGE=binance

# Trading Mode (true = safe paper trading, false = live trading)
PAPER_TRADING=true

# Strategy Enablement
TRIFECTA_TRADING_ENABLED=true
SQUEEZE_TRADING_ENABLED=true

# ================================
# RISK MANAGEMENT
# ================================

# Trifecta Strategy (Short positions)
TRIFECTA_MAX_POSITION=1000
TRIFECTA_STOP_LOSS=2.0
TRIFECTA_TAKE_PROFIT=5.0

# Squeeze Strategy (Long positions)
SQUEEZE_MAX_POSITION=500
SQUEEZE_STOP_LOSS=1.5
SQUEEZE_TAKE_PROFIT=3.0
SQUEEZE_TIME_EXIT=300

# ================================
# CLASSIFICATION THRESHOLDS
# ================================

# Market pressure threshold (ask/bid ratio)
PRESSURE_THRESHOLD=3.0

# Liquidity threshold (minimum bid volume)
LIQUIDITY_THRESHOLD=100000

# Momentum thresholds (percentage)
STRONG_MOMENTUM_THRESHOLD=-0.3
WEAK_MOMENTUM_THRESHOLD=-0.1

# ================================
# SYSTEM CONFIGURATION
# ================================

# Alert cooldown period (minutes)
COOLDOWN_MINUTES=5

# Order book analysis depth
ORDER_BOOK_DEPTH=50

# Logging level (error, warn, info, debug)
LOG_LEVEL=info

# Environment mode
NODE_ENV=development

# Server port
PORT=3000
